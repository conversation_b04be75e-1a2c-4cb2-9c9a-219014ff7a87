
<?php
    use App\Models\Utility;
    $logo = \App\Models\Utility::get_file('uploads/logo');
    $settings = Utility::settings();
    $company_logo = $settings['company_logo'] ?? '';
    $lang = app()->getLocale();
?>

<?php $__env->startPush('custom-scripts'); ?>
    <?php if($settings['recaptcha_module'] == 'on'): ?>
        <?php echo NoCaptcha::renderJs(); ?>

    <?php endif; ?>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const passwordInput = document.getElementById('input-password');
            const togglePassword = document.getElementById('toggle-password');
            const toggleIcon = document.getElementById('toggle-password-icon');
            if (togglePassword) {
                togglePassword.addEventListener('click', function () {
                    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordInput.setAttribute('type', type);
                    if (type === 'text') {
                        toggleIcon.classList.remove('ti-eye');
                        toggleIcon.classList.add('ti-eye-off');
                    } else {
                        toggleIcon.classList.remove('ti-eye-off');
                        toggleIcon.classList.add('ti-eye');
                    }
                });
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Login')); ?>

<?php $__env->stopSection(); ?>

<?php if($settings['cust_darklayout'] == 'on'): ?>
    <style>
        .g-recaptcha {
            filter: invert(1) hue-rotate(180deg) !important;
        }
    </style>
<?php endif; ?>

<?php $__env->startSection('content'); ?>
    <div class="text-center mb-4">
        <h3 class="fw-bold mb-1" style="color:#22c55e"><i class="ti ti-lock"></i> Sign In</h3>
        <div class="text-muted mb-3" style="font-size:1.05rem;">Access your AI automation dashboard</div>
    </div>
    <?php echo e(Form::open(['route' => 'login', 'method' => 'post', 'id' => 'loginForm', 'class' => 'login-form needs-validation', 'novalidate'])); ?>

    <?php if(session('status')): ?>
        <div class="mb-4 font-medium text-lg text-danger">
            <?php echo e(session('status')); ?>

        </div>
    <?php endif; ?>
    <div class="mb-3">
        <label class="form-label text-light-emphasis small mb-1">Email Address</label>
        <div class="input-group rounded-3 bg-dark-subtle">
            <span class="input-group-text bg-transparent border-0"><i class="ti ti-mail text-success"></i></span>
            <?php echo e(Form::text('email', null, ['class' => 'form-control bg-transparent border-0 text-light', 'placeholder' => 'Enter your email', 'required' => 'required', 'autocomplete' => 'email'])); ?>

        </div>
        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <span class="error invalid-email text-danger small" role="alert">
                <strong><?php echo e($message); ?></strong>
            </span>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>
    <div class="mb-2">
        <label class="form-label text-light-emphasis small mb-1">Password</label>
        <div class="input-group rounded-3 bg-dark-subtle">
            <span class="input-group-text bg-transparent border-0">
                <i class="ti ti-lock text-success"></i>
            </span>
            <?php echo e(Form::password('password', [
                'class' => 'form-control bg-transparent border-0 text-light',
                'placeholder' => 'Enter your password',
                'id' => 'input-password',
                'required' => 'required',
                'autocomplete' => 'current-password'
            ])); ?>

            <span class="input-group-text bg-transparent border-0" style="cursor:pointer;" id="toggle-password">
                <i class="ti ti-eye" id="toggle-password-icon"></i>
            </span>
        </div>
        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <span class="error invalid-password text-danger small" role="alert">
                <strong><?php echo e($message); ?></strong>
            </span>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>
    <div class="d-flex justify-content-end mb-3">
        <!-- <?php if(Route::has('password.request')): ?>
            <a href="<?php echo e(route('password.request', $lang)); ?>" class="text-success small">Forgot password?</a>
        <?php endif; ?> -->
    </div>
    <?php if($settings['recaptcha_module'] == 'on'): ?>
        <?php if(isset($settings['google_recaptcha_version']) && $settings['google_recaptcha_version'] == 'v2-checkbox'): ?>
            <div class="form-group col-lg-12 col-md-12 mt-3">
                <?php echo NoCaptcha::display(); ?>

                <?php $__errorArgs = ['g-recaptcha-response'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <span class="small text-danger" role="alert">
                        <strong><?php echo e($message); ?></strong>
                    </span>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        <?php else: ?>
            <div class="form-group col-lg-12 col-md-12 mt-3">
                <input type="hidden" id="g-recaptcha-response" name="g-recaptcha-response" class="form-control">
                <?php $__errorArgs = ['g-recaptcha-response'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <span class="error small text-danger" role="alert">
                        <strong><?php echo e($message); ?></strong>
                    </span>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
    <div class="d-grid mb-3">
        <?php echo e(Form::submit(__('Sign In'), ['class' => 'btn btn-success btn-lg rounded-3 fw-bold', 'id' => 'saveBtn', 'style' => 'box-shadow:0 2px 8px 0 rgba(34,197,94,0.15);'])); ?>

    </div>

    <div class="d-flex align-items-center justify-content-center mt-3 mb-1">
        <span class="text-success me-2"><i class="ti ti-lock"></i></span>
        <span class="small text-muted">Secured with enterprise-grade encryption</span>
    </div>
    <?php echo e(Form::close()); ?>

<?php $__env->stopSection(); ?>


<?php echo $__env->make('layouts.auth', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/auth/login.blade.php ENDPATH**/ ?>